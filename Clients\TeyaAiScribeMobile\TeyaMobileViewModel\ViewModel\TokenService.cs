﻿using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Components;

namespace TeyaMobileViewModel.ViewModel
{
    public class TokenService : ITokenService
    {
        private readonly IStringLocalizer<TokenService> _localizer;
        private readonly NavigationManager _navigationManager;

        public TokenService(
            IStringLocalizer<TokenService> localizer,
            NavigationManager navigationManager)
        {
            _localizer = localizer;
            _navigationManager = navigationManager;
        }

        // ✅ Get/Set AccessToken from Secure Storage (for mobile)
        public string? AccessToken
        {
            get => SecureStorage.GetAsync("AccessToken").Result;
            set => _ = SecureStorage.SetAsync("AccessToken", value ?? "");
        }

        public string? AccessToken2
        {
            get => SecureStorage.GetAsync("AccessToken2").Result;
            set => _ = SecureStorage.SetAsync("AccessToken2", value ?? "");
        }

        public string? UserDetails
        {
            get => SecureStorage.GetAsync("UserDetails").Result;
            set => _ = SecureStorage.SetAsync("UserDetails", value ?? "");
        }

        public async Task<string?> GetValidatedAccessTokenAsync()
        {
            var token = await SecureStorage.GetAsync("AccessToken");
            if (string.IsNullOrEmpty(token))
            {
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }
            return token;
        }

        public async Task<string?> GetValidatedAccessToken2Async()
        {
            var token = await SecureStorage.GetAsync("AccessToken2");
            if (string.IsNullOrEmpty(token))
            {
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }
            return token;
        }

        public Task<string> GetAccessTokenAsync()
        {
            throw new NotImplementedException();
        }
    }
}
