using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.IdentityModel.Tokens.Jwt;
using TeyaMobile.Shared.Services;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Services
{
    /// <summary>
    /// Simplified Android authentication service using MSAL
    /// </summary>
    public class SimpleAndroidAuthenticationService : IAuthenticationService
    {
        private readonly IPublicClientApplication _publicClientApp;
        private readonly ILogger<SimpleAndroidAuthenticationService> _logger;
        private readonly IConfiguration _configuration;

        private string[] _scopes;

        public SimpleAndroidAuthenticationService(
            IPublicClientApplication publicClientApp,
            ILogger<SimpleAndroidAuthenticationService> logger,
            IConfiguration configuration)
        {
            _publicClientApp = publicClientApp;
            _logger = logger;
            _configuration = configuration;
            
            // Get scopes from configuration
            var azureAdSection = _configuration.GetSection("AzureAd");
            var scopesConfig = azureAdSection.GetSection("Scopes").Get<string[]>();
            _scopes = scopesConfig ?? new[] { "openid" };
        }

        public bool IsAuthenticated
        {
            get
            {
                try
                {
                    var accounts = _publicClientApp.GetAccountsAsync().Result;
                    return accounts.Any();
                }
                catch
                {
                    return false;
                }
            }
        }

        public async Task<bool> LoginAsync()
        {
            try
            {
                // Try silent authentication first
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (accounts.Any())
                {
                    try
                    {
                        var result = await _publicClientApp.AcquireTokenSilent(_scopes, accounts.FirstOrDefault())
                            .ExecuteAsync();
                        return true;
                    }
                    catch (MsalUiRequiredException)
                    {
                        // Silent auth failed, need interactive auth
                    }
                }

                // Interactive authentication
                var authResult = await _publicClientApp.AcquireTokenInteractive(_scopes)
                    .WithPrompt(Prompt.SelectAccount)
                    .ExecuteAsync();

                return authResult != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed: {Error}", ex.Message);
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                foreach (var account in accounts)
                {
                    await _publicClientApp.RemoveAsync(account);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Logout failed: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (!accounts.Any())
                    return string.Empty;

                var result = await _publicClientApp.AcquireTokenSilent(_scopes, accounts.FirstOrDefault())
                    .ExecuteAsync();

                return result.AccessToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get access token: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetUserNameAsync()
        {
            try
            {
                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                    return string.Empty;

                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(accessToken);

                return jsonToken.Claims.FirstOrDefault(c => c.Type == "name")?.Value ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user name: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetUserEmailAsync()
        {
            try
            {
                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                    return string.Empty;

                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(accessToken);

                return jsonToken.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ??
                       jsonToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value ?? 
                       string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user email: {Error}", ex.Message);
                return string.Empty;
            }
        }
    }
}
