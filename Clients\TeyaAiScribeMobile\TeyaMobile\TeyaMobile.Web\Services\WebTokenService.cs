using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Components;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Web.Services
{
    /// <summary>
    /// Web TokenService using session storage (like <PERSON>yaWebApp)
    /// </summary>
    public class WebTokenService : ITokenService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IStringLocalizer<WebTokenService> _localizer;
        private readonly NavigationManager _navigationManager;

        public WebTokenService(
            IHttpContextAccessor httpContextAccessor,
            IStringLocalizer<WebTokenService> localizer,
            NavigationManager navigationManager)
        {
            _httpContextAccessor = httpContextAccessor;
            _localizer = localizer;
            _navigationManager = navigationManager;
        }

        // ✅ Get/Set AccessToken from Session (for web)
        public string? AccessToken
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("AccessToken");
            set => _httpContextAccessor.HttpContext?.Session.SetString("AccessToken", value ?? "");
        }

        public string? AccessToken2
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("AccessToken2");
            set => _httpContextAccessor.HttpContext?.Session.SetString("AccessToken2", value ?? "");
        }

        public string? UserDetails
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("UserDetails");
            set => _httpContextAccessor.HttpContext?.Session.SetString("UserDetails", value ?? "");
        }

        public async Task<string?> GetValidatedAccessTokenAsync()
        {
            var token = AccessToken;
            if (string.IsNullOrEmpty(token))
            {
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }
            return token;
        }

        public async Task<string?> GetValidatedAccessToken2Async()
        {
            var token = AccessToken2;
            if (string.IsNullOrEmpty(token))
            {
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }
            return token;
        }
    }
}
