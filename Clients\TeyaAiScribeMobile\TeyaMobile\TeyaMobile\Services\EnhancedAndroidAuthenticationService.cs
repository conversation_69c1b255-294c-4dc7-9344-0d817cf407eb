using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.IdentityModel.Tokens.Jwt;
using TeyaMobile.Shared.Services;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Services
{
    /// <summary>
    /// Enhanced Android authentication service using MSAL + TeyaWebApp OBO flow
    /// </summary>
    public class EnhancedAndroidAuthenticationService : IAuthenticationService
    {
        private readonly IPublicClientApplication _publicClientApp;
        private readonly ILogger<EnhancedAndroidAuthenticationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ITokenService _tokenService;

        private string[] _scopes;

        public EnhancedAndroidAuthenticationService(
            IPublicClientApplication publicClientApp,
            ILogger<EnhancedAndroidAuthenticationService> logger,
            IConfiguration configuration,
            ITokenService tokenService)
        {
            _publicClientApp = publicClientApp;
            _logger = logger;
            _configuration = configuration;
            _tokenService = tokenService;
            
            // Get scopes from configuration
            var azureAdSection = _configuration.GetSection("AzureAd");
            var scopesConfig = azureAdSection.GetSection("Scopes").Get<string[]>();
            _scopes = scopesConfig ?? new[] { "openid" };
        }

        public bool IsAuthenticated
        {
            get
            {
                try
                {
                    var accounts = _publicClientApp.GetAccountsAsync().Result;
                    return accounts.Any();
                }
                catch
                {
                    return false;
                }
            }
        }

        public async Task<bool> LoginAsync()
        {
            try
            {
                // Try silent authentication first
                var accounts = await _publicClientApp.GetAccountsAsync();
                AuthenticationResult authResult = null;

                if (accounts.Any())
                {
                    try
                    {
                        authResult = await _publicClientApp.AcquireTokenSilent(_scopes, accounts.FirstOrDefault())
                            .ExecuteAsync();
                    }
                    catch (MsalUiRequiredException)
                    {
                        // Silent auth failed, need interactive auth
                    }
                }

                // Interactive authentication if silent failed
                if (authResult == null)
                {
                    authResult = await _publicClientApp.AcquireTokenInteractive(_scopes)
                        .WithPrompt(Prompt.SelectAccount)
                        .ExecuteAsync();
                }

                if (authResult != null)
                {
                    // Now perform OBO flow like TeyaWebApp
                    await PerformOnBehalfOfFlow(authResult.AccessToken);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed: {Error}", ex.Message);
                return false;
            }
        }

        private async Task PerformOnBehalfOfFlow(string accessToken)
        {
            try
            {
                var clientId = Environment.GetEnvironmentVariable("AUTH_CLIENT_ID");
                var clientSecret = Environment.GetEnvironmentVariable("AUTH_CLIENT_SECRET");
                var authority = Environment.GetEnvironmentVariable("AUTH_AUTHORITY");

                var confidentialClientApp = ConfidentialClientApplicationBuilder
                    .Create(clientId)
                    .WithClientSecret(clientSecret)
                    .WithAuthority(new Uri(authority))
                    .Build();

                // Get user access token using OBO flow
                var userAccessTokenResult = await confidentialClientApp.AcquireTokenOnBehalfOf(
                    new[] { Environment.GetEnvironmentVariable("AUTH_SCOPE_0") },
                    new UserAssertion(accessToken)
                ).ExecuteAsync();

                // Get Graph API token using OBO flow
                var graphAccessTokenResult = await confidentialClientApp.AcquireTokenOnBehalfOf(
                    new[] { Environment.GetEnvironmentVariable("AUTH_SCOPE_4") ?? "https://graph.microsoft.com/.default" },
                    new UserAssertion(accessToken)
                ).ExecuteAsync();

                // Store tokens using TokenService (which will use secure storage)
                _tokenService.AccessToken = userAccessTokenResult.AccessToken;
                _tokenService.AccessToken2 = graphAccessTokenResult.AccessToken;

                _logger.LogInformation("OBO flow completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "OBO flow failed: {Error}", ex.Message);
                // Store the original token if OBO fails
                _tokenService.AccessToken = accessToken;
            }
        }

        public async Task LogoutAsync()
        {
            try
            {
                var accounts = await _publicClientApp.GetAccountsAsync();
                foreach (var account in accounts)
                {
                    await _publicClientApp.RemoveAsync(account);
                }

                // Clear stored tokens
                _tokenService.AccessToken = null;
                _tokenService.AccessToken2 = null;
                _tokenService.UserDetails = null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Logout failed: {Error}", ex.Message);
                throw;
            }
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                // First try to get from TokenService
                var storedToken = _tokenService.AccessToken;
                if (!string.IsNullOrEmpty(storedToken))
                {
                    return storedToken;
                }

                // If no stored token, try to get fresh token
                var accounts = await _publicClientApp.GetAccountsAsync();
                if (!accounts.Any())
                    return string.Empty;

                var result = await _publicClientApp.AcquireTokenSilent(_scopes, accounts.FirstOrDefault())
                    .ExecuteAsync();

                // Perform OBO flow with fresh token
                await PerformOnBehalfOfFlow(result.AccessToken);

                return _tokenService.AccessToken ?? result.AccessToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get access token: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetUserNameAsync()
        {
            try
            {
                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                    return string.Empty;

                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(accessToken);

                return jsonToken.Claims.FirstOrDefault(c => c.Type == "name")?.Value ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user name: {Error}", ex.Message);
                return string.Empty;
            }
        }

        public async Task<string> GetUserEmailAsync()
        {
            try
            {
                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                    return string.Empty;

                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(accessToken);

                return jsonToken.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value ??
                       jsonToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value ?? 
                       string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get user email: {Error}", ex.Message);
                return string.Empty;
            }
        }
    }
}
