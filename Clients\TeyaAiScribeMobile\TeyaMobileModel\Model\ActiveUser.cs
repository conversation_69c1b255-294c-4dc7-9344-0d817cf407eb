﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace TeyaMobileModel.Model
{
    public class ActiveUser
    {
        public string id { get; set; }
        public string displayName { get; set; }
        public string givenName { get; set; }
        public string surname { get; set; }
        public string userType { get; set; }
        public string jobTitle { get; set; }
        public string companyName { get; set; }
        public string department { get; set; }
        public string officeLocation { get; set; }
        public string streetAddress { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string postalCode { get; set; }
        public string country { get; set; }
        public string role { get; set; }
        public List<string> businessPhones { get; set; } = new List<string>();

        public string mobilePhone { get; set; }
        public string mail { get; set; }

        [JsonPropertyName("extension_8a2d87f30a864e7e8b70f49083a6ff68_OrganizationName")]
        public string OrganizationName { get; set; }

        [JsonPropertyName("extension_8a2d87f30a864e7e8b70f49083a6ff68_Address")]
        public string Address { get; set; }

        [JsonIgnore]
        public string oDataContext { get; set; }
    }
}
