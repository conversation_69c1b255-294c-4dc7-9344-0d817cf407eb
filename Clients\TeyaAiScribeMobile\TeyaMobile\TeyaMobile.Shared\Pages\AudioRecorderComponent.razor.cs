﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Microsoft.Extensions.Logging;
using System.Timers;
using TeyaMobile.Shared.Scripts;
using TeyaMobileModel.Model;
using TeyaMobileViewModel.ViewModel;

namespace TeyaMobile.Shared.Pages
{
    public partial class AudioRecorderComponent : ComponentBase, IDisposable
    {
        // Make AudioRecorder nullable to handle web platform where it's not available
        [Inject] private IAudioRecorder? AudioRecorder { get; set; }
        [Inject] private ILogger<AudioRecorderComponent>? Logger { get; set; }

        // Platform detection properties
        private bool IsWeb => OperatingSystem.IsBrowser();
        private bool IsAndroid => OperatingSystem.IsAndroid();
        private bool IsIOS => OperatingSystem.IsIOS();
        private bool IsNativePlatform => IsAndroid || IsIOS;

        private string LiveTranscriptionText = "";

        private ElementReference transcriptionEditor;

        // Component state
        private bool IsRecording = false;
        private bool IsPaused = false;
        private bool IsProcessing = false;
        private int RecordingDuration = 0;
        private string TranscriptionText = "";
        private Guid CurrentRecordingId = Guid.Empty;

        // Platform-specific helpers
        private AudioRecorderInterop? _audioInterop;
        private System.Timers.Timer? _durationTimer;
        private System.Timers.Timer? _animationTimer;
        private Random _random = new Random();
        private DotNetObjectReference<AudioRecorderComponent>? _dotNetRef;
        private DateTime _recordingStartTime;
        private TimeSpan _totalPausedDuration = TimeSpan.Zero;
        private DateTime? _pauseStartTime;

        // Parameters for backend operations
        [Parameter] public Guid? PatientId { get; set; } = Guid.NewGuid();
        [Parameter] public string VisitType { get; set; } = "Unknown";
        [Parameter] public Guid? OrganizationId { get; set; } = Guid.NewGuid();
        [Parameter] public bool Subscription { get; set; } = false;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                _dotNetRef = DotNetObjectReference.Create(this);
                Logger?.LogInformation($"AudioRecorderComponent initializing. Platform: Web={IsWeb}, Android={IsAndroid}, iOS={IsIOS}");

                // Only register AudioRecorder events if it exists (native platforms)
                if (IsNativePlatform && AudioRecorder != null)
                {
                    AudioRecorder.RecordingStateChanged += OnRecordingStateChanged;
                    AudioRecorder.ErrorOccurred += OnErrorOccurred;
                    Logger?.LogInformation("AudioRecorder events registered for native platform");
                }
                else if (IsNativePlatform && AudioRecorder == null)
                {
                    Logger?.LogWarning("AudioRecorder service not available on native platform");
                }

                if (SpeechService != null)
                {
                    SpeechService.OnPartialTranscription += OnPartialTranscription;
                    SpeechService.OnFinalTranscription += OnFinalTranscription;
                    SpeechService.OnError += OnSpeechError;
                    Logger?.LogInformation("SpeechService events registered");
                }
                else
                {
                    Logger?.LogWarning("SpeechService not available");
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error during AudioRecorderComponent initialization");
                await HandleError($"Initialization failed: {ex.Message}");
            }
        }

        private void OnPartialTranscription(object? sender, string text)
        {
            LiveTranscriptionText = text;
            TranscriptionText += text + " ";
            InvokeAsync(StateHasChanged);
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && IsWeb && JSRuntime != null)
            {
                try
                {
                    _audioInterop = new AudioRecorderInterop(JSRuntime, this);
                    await _audioInterop.InitializeAsync();
                    Logger?.LogInformation("Web audio interop initialized");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "Failed to initialize web audio interop");
                    await HandleError($"Web audio initialization failed: {ex.Message}");
                }
            }
        }

        private async Task StartRecording()
        {
            try
            {
                Logger?.LogInformation("Starting recording...");
                IsProcessing = true;
                StateHasChanged();

                CurrentRecordingId = Guid.NewGuid();
                TranscriptionText = "";
                _recordingStartTime = DateTime.Now;
                _totalPausedDuration = TimeSpan.Zero;
                _pauseStartTime = null;

                if (IsWeb)
                {
                    await StartWebRecording();
                }
                else if (IsNativePlatform)
                {
                    await StartNativeRecording();
                }

                StartTimers();
                IsRecording = true;
                IsPaused = false;
                IsProcessing = false;

                await StartSpeechRecognition();

                Logger?.LogInformation($"Recording started successfully with ID: {CurrentRecordingId}");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to start recording");
                IsProcessing = false;
                await HandleError($"Failed to start recording: {ex.Message}");
            }
        }

        private async Task StartWebRecording()
        {
            if (_audioInterop != null)
            {
                await _audioInterop.StartRecordAsync();
                Logger?.LogInformation("Web recording started");
            }
            else
            {
                throw new InvalidOperationException("Web audio interop not initialized");
            }
        }

        private async Task StartNativeRecording()
        {
            if (AudioRecorder != null)
            {
                AudioRecorder.SetNextRecordingId(CurrentRecordingId);
                await AudioRecorder.StartRecordingAsync();
                Logger?.LogInformation("Native recording started");
            }
            else
            {
                throw new InvalidOperationException("AudioRecorder service not available");
            }
        }

        private async Task StartSpeechRecognition()
        {
            if (SpeechService != null)
            {
                if (IsWeb)
                {
                    await SpeechService.StartTranscriptionAsync(CurrentRecordingId);
                }
                else
                {
                    await SpeechService.StartContinuousRecognitionAsync();
                }
                Logger?.LogInformation("Speech recognition started");
            }
        }

        private async Task PauseResumeRecording()
        {
            try
            {
                if (IsPaused)
                {
                    await ResumeRecording();
                }
                else
                {
                    await PauseRecording();
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to pause/resume recording");
                await HandleError($"Failed to pause/resume recording: {ex.Message}");
            }
        }

        private async Task PauseRecording()
        {
            Logger?.LogInformation("Pausing recording...");
            _pauseStartTime = DateTime.Now;

            if (IsWeb && _audioInterop != null)
            {
                await _audioInterop.PauseRecordAsync();
            }
            else if (IsNativePlatform && AudioRecorder != null)
            {
                await AudioRecorder.PauseRecordingAsync();
            }

            if (SpeechService != null && !IsWeb)
            {
                await SpeechService.StopContinuousRecognitionAsync();
            }

            IsPaused = true;
            _durationTimer?.Stop();
            Logger?.LogInformation("Recording paused");
            StateHasChanged();
        }

        private async Task ResumeRecording()
        {
            Logger?.LogInformation("Resuming recording...");

            if (_pauseStartTime.HasValue)
            {
                _totalPausedDuration += DateTime.Now - _pauseStartTime.Value;
                _pauseStartTime = null;
            }

            if (IsWeb && _audioInterop != null)
            {
                await _audioInterop.ResumeRecordAsync();
            }
            else if (IsNativePlatform && AudioRecorder != null)
            {
                await AudioRecorder.ResumeRecordingAsync();
            }

            if (SpeechService != null && !IsWeb)
            {
                await SpeechService.StartContinuousRecognitionAsync();
            }

            IsPaused = false;
            _durationTimer?.Start();
            Logger?.LogInformation("Recording resumed");
            StateHasChanged();
        }

        private async Task StopRecording()
        {
            try
            {
                Logger?.LogInformation("Stopping recording...");
                IsProcessing = true;
                StateHasChanged();

                StopTimers();

                string filePath = "";

                if (IsWeb)
                {
                    await StopWebRecording();
                }
                else if (IsNativePlatform)
                {
                    filePath = await StopNativeRecording();
                }

                // Process recording completion and upload to backend
                await ProcessRecordingCompletion(filePath);

                IsRecording = false;
                IsPaused = false;
                RecordingDuration = 0;
                IsProcessing = false;
                Logger?.LogInformation("Recording stopped successfully");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to stop recording");
                IsProcessing = false;
                await HandleError($"Failed to stop recording: {ex.Message}");
            }
        }

        // In AudioRecorderComponent.razor.cs
        private async Task StopWebRecording()
        {
            if (_audioInterop != null)
            {
                await _audioInterop.StopRecordAsync(CurrentRecordingId);
                Logger?.LogInformation("Web recording stopped");
            }
        }


        private async Task<string> StopNativeRecording()
        {
            string filePath = "";

            if (AudioRecorder != null)
            {
                filePath = await AudioRecorder.StopRecordingAsync();
                Logger?.LogInformation($"Native recording stopped. File path: {filePath}");
            }
            else
            {
                Logger?.LogWarning("AudioRecorder not available for stopping native recording");
            }

            return filePath;
        }

        private async Task ProcessRecordingCompletion(string filePath)
        {
            if (SpeechService == null)
            {
                Logger?.LogWarning("SpeechService not available for processing recording completion");
                return;
            }

            try
            {
                Logger?.LogInformation("Processing recording completion...");

                if (IsWeb)
                {
                    // For web, stop transcription and let JavaScript handle upload
                    await SpeechService.StopTranscriptionAsync(
                        CurrentRecordingId,
                        PatientId ?? Guid.NewGuid(),
                        VisitType,
                        OrganizationId,
                        Subscription
                    );
                    Logger?.LogInformation("Web transcription stopped and processed");
                }
                else if (IsNativePlatform)
                {
                    // For native platforms, stop continuous recognition first
                    await SpeechService.StopContinuousRecognitionAsync();

                    // Upload audio file and post transcriptions for native platforms
                    if (!string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath))
                    {
                        Logger?.LogInformation($"Uploading audio file: {filePath}");
                        await SpeechService.UploadAudioToBackendAsync(filePath);
                        Logger?.LogInformation("Audio file uploaded successfully");

                        Logger?.LogInformation("Posting transcriptions to backend");
                        await SpeechService.PostTranscriptionsAsync(
                            CurrentRecordingId,
                            PatientId ?? Guid.NewGuid(),
                            VisitType,
                            OrganizationId,
                            Subscription
                        );
                        Logger?.LogInformation("Transcriptions posted successfully");
                    }
                    else
                    {
                        Logger?.LogWarning($"Audio file not found or empty path: {filePath}. Posting transcriptions without file.");
                        // Even if no file, still post transcriptions from continuous recognition
                        await SpeechService.PostTranscriptionsAsync(
                            CurrentRecordingId,
                            PatientId ?? Guid.NewGuid(),
                            VisitType,
                            OrganizationId,
                            Subscription
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to process recording completion");
                await HandleError($"Failed to process recording completion: {ex.Message}");
            }
        }

        private TimeSpan GetEffectiveRecordingDuration()
        {
            var totalDuration = DateTime.Now - _recordingStartTime;
            var effectiveDuration = totalDuration - _totalPausedDuration;

            if (_pauseStartTime.HasValue)
            {
                var currentPauseDuration = DateTime.Now - _pauseStartTime.Value;
                effectiveDuration -= currentPauseDuration;
            }

            return effectiveDuration;
        }

        private void StartTimers()
        {
            RecordingDuration = 0;
            _durationTimer = new System.Timers.Timer(1000);
            _durationTimer.Elapsed += OnDurationTimerElapsed;
            _durationTimer.Start();

            if (IsWeb)
            {
                _animationTimer = new System.Timers.Timer(100);
                _animationTimer.Elapsed += OnAnimationTimerElapsed;
                _animationTimer.Start();
            }
        }

        private void StopTimers()
        {
            _durationTimer?.Stop();
            _durationTimer?.Dispose();
            _durationTimer = null;

            _animationTimer?.Stop();
            _animationTimer?.Dispose();
            _animationTimer = null;
        }

        private void OnDurationTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            RecordingDuration++;
            InvokeAsync(StateHasChanged);
        }

        private void OnAnimationTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            InvokeAsync(StateHasChanged);
        }

        // Event handlers for native platforms
        private void OnRecordingStateChanged(object? sender, RecordingState state)
        {
            Logger?.LogInformation($"Recording state changed: {state}");
            InvokeAsync(StateHasChanged);
        }

        private void OnErrorOccurred(object? sender, Exception ex)
        {
            Logger?.LogError(ex, "AudioRecorder error occurred");
            InvokeAsync(() => HandleError($"Recording error: {ex.Message}"));
        }

        private void OnFinalTranscription(object? sender, string text)
        {
            Logger?.LogInformation($"Final transcription received: {text}");
        }

        private void OnSpeechError(object? sender, Exception ex)
        {
            Logger?.LogError(ex, "Speech recognition error");
            InvokeAsync(() => HandleError($"Speech recognition error: {ex.Message}"));
        }

        // JSInvokable methods for web platform
        [JSInvokable]
        public async Task ProcessAudioChunk(string base64AudioChunk)
        {
            if (SpeechService != null)
            {
                await SpeechService.ProcessAudioChunk(base64AudioChunk);
            }
        }

        [JSInvokable]
        public async Task OnRecordingComplete(string recordId)
        {
            Logger?.LogInformation($"Web recording completed: {recordId}");
        }

        [JSInvokable]
        public async Task OnAudioDetected(bool isDetected)
        {
            await InvokeAsync(StateHasChanged);
        }

        [JSInvokable]
        public async Task OnRecordingError(string error)
        {
            Logger?.LogError("Web recording error: {Error}", error);
            await HandleError($"Web recording error: {error}");
        }

        // Helper Methods
        private string GetStatusClass()
        {
            if (IsProcessing) return "status-processing";
            if (IsRecording && IsPaused) return "status-paused";
            if (IsRecording) return "status-recording";
            return "status-ready";
        }

        private string GetStatusText()
        {
            if (IsProcessing) return "Processing...";
            if (IsRecording && IsPaused) return "Paused";
            if (IsRecording) return "Recording...";
            return "Teya AI Scribe";
        }

        private string GetWaveBarStyle(int index)
        {
            if (!IsRecording || IsPaused)
            {
                return "--height: 20px;";
            }

            var height = _random.Next(20, 80);
            return $"--height: {height}px;";
        }

        private string FormatDuration(int seconds)
        {
            var timeSpan = TimeSpan.FromSeconds(seconds);
            return timeSpan.ToString(@"mm\:ss");
        }

        private async Task HandleError(string message)
        {
            Logger?.LogError("AudioRecorder Error: {Message}", message);
            Console.WriteLine($"Audio Recorder Error: {message}");
            IsProcessing = false;
            StateHasChanged();
        }

        public void Dispose()
        {
            try
            {
                StopTimers();

                // Only unregister if AudioRecorder exists and we're on native platform
                if (IsNativePlatform && AudioRecorder != null)
                {
                    AudioRecorder.RecordingStateChanged -= OnRecordingStateChanged;
                    AudioRecorder.ErrorOccurred -= OnErrorOccurred;
                }

                if (SpeechService != null)
                {
                    SpeechService.OnPartialTranscription -= OnPartialTranscription;
                    SpeechService.OnFinalTranscription -= OnFinalTranscription;
                    SpeechService.OnError -= OnSpeechError;
                }

                _audioInterop?.Dispose();
                _dotNetRef?.Dispose();

                Logger?.LogInformation("AudioRecorderComponent disposed successfully");
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error during AudioRecorderComponent disposal");
            }
        }
    }
}
