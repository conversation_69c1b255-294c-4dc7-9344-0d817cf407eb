using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Components.Server.Circuits;
using Microsoft.AspNetCore.Components.Server;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;
using Syncfusion.Blazor;
using TeyaMobile.Shared.Pages;
using TeyaMobile.Shared.Services;
using TeyaMobile.Shared.Services.TeyaMobile.Shared.Services;
using TeyaMobile.Web.Components;
using TeyaMobile.Web.Services;
using TeyaMobileViewModel.ViewModel;
using Syncfusion.Licensing;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Identity.Client;

var builder = WebApplication.CreateBuilder(args);

var syncfusionKey = builder.Configuration["SyncfusionKey"];
if (!string.IsNullOrEmpty(syncfusionKey))
{
    SyncfusionLicenseProvider.RegisterLicense(syncfusionKey);
}

builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Add session support for token storage
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(60);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// TeyaWebApp authentication approach
builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
})
.AddCookie()
.AddOpenIdConnect(options =>
{
    options.ClientId = Environment.GetEnvironmentVariable("AUTH_CLIENT_ID");
    options.Authority = Environment.GetEnvironmentVariable("AUTH_AUTHORITY");
    options.ClientSecret = Environment.GetEnvironmentVariable("AUTH_CLIENT_SECRET");
    options.ResponseType = Environment.GetEnvironmentVariable("AUTH_RESPONSE_TYPE");
    options.SaveTokens = bool.Parse(Environment.GetEnvironmentVariable("AUTH_SAVE_TOKENS") ?? "false");
    options.CallbackPath = Environment.GetEnvironmentVariable("AUTH_CALLBACK_PATH");
    options.Scope.Add(Environment.GetEnvironmentVariable("AUTH_SCOPE_0"));
    options.Scope.Add(Environment.GetEnvironmentVariable("AUTH_SCOPE_1"));
    options.Scope.Add(Environment.GetEnvironmentVariable("AUTH_SCOPE_2"));

    options.TokenValidationParameters = new TokenValidationParameters
    {
        NameClaimType = "name",
        RoleClaimType = "roles"
    };

    options.Events = new OpenIdConnectEvents
    {
        OnTokenResponseReceived = async context =>
        {
            var session = context.HttpContext.Session;

            var confidentialClientApp = ConfidentialClientApplicationBuilder
                .Create(options.ClientId)
                .WithClientSecret(options.ClientSecret)
                .WithAuthority(new Uri(options.Authority))
                .Build();

            var userAccessTokenResult = await confidentialClientApp.AcquireTokenOnBehalfOf(
                new[] { Environment.GetEnvironmentVariable("AUTH_SCOPE_0") },
                new UserAssertion(context.TokenEndpointResponse.AccessToken)
            ).ExecuteAsync();

            var graphAccessTokenResult = await confidentialClientApp.AcquireTokenOnBehalfOf(
                new[] { Environment.GetEnvironmentVariable("AUTH_SCOPE_4") },
                new UserAssertion(context.TokenEndpointResponse.AccessToken)
            ).ExecuteAsync();

            // Store tokens in Session Storage
            session.SetString("AccessToken", userAccessTokenResult.AccessToken);
            session.SetString("AccessToken2", graphAccessTokenResult.AccessToken);
        }
    };
});

// Add Razor Pages for authentication
builder.Services.AddRazorPages();

// Authorization
builder.Services.AddAuthorization();

// HTTP Context Accessor
builder.Services.AddHttpContextAccessor();

builder.Services.AddHttpClient();
builder.Services.AddLocalization();
builder.Services.AddScoped<ISpeechService, SpeechService>();
builder.Services.AddScoped<AudioRecorderComponent>();
builder.Services.AddScoped<IAudioRecorder, DummyAudioRecorder>();
builder.Services.AddScoped<IAppointmentService, AppointmentService>();
builder.Services.AddScoped<IProgressNotesService, ProgressNotesService>();

// Authentication service for web
builder.Services.AddScoped<TeyaMobileViewModel.ViewModel.IAuthenticationService, SimpleWebAuthenticationService>();

// TokenService using TeyaWebApp approach (session-based for web)
builder.Services.AddScoped<TeyaMobileViewModel.ViewModel.ITokenService, WebTokenService>();

builder.Services.AddSingleton<IFormFactor, FormFactor>();
builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();
builder.Services.AddSyncfusionBlazor();

// Enhanced logging for SignalR and Blazor Server debugging
builder.Logging.AddConsole();
if (builder.Environment.IsDevelopment())
{
    builder.Logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Debug);
    builder.Logging.AddFilter("Microsoft.AspNetCore.SignalR", Microsoft.Extensions.Logging.LogLevel.Debug);
    builder.Logging.AddFilter("Microsoft.AspNetCore.Http.Connections", Microsoft.Extensions.Logging.LogLevel.Debug);
    builder.Logging.AddFilter("Microsoft.AspNetCore.Components.Server.Circuits", Microsoft.Extensions.Logging.LogLevel.Debug);
}

var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// Session middleware
app.UseSession();

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddAdditionalAssemblies(typeof(TeyaMobile.Shared._Imports).Assembly);

// Map Microsoft Identity routes for authentication
app.MapControllers();
app.MapRazorPages();

app.Run();
